import { Button } from '@/components/ui/button';
import type { BusinessPartnerDto, MasterTenantDto } from '@/clientEkb/types.gen';
import React from 'react';
import { createRoot } from 'react-dom/client';
import type { Root } from 'react-dom/client';
import { AttachmentDialog } from '../attachment-dialog';
import type { QueryClient } from '@tanstack/react-query';
import Handsontable from 'handsontable';

function getRootContainer(td: unknown): Root | undefined {
  return typeof td === 'object' && td !== null && '_reactRootContainer' in td
    ? (td as { _reactRootContainer?: Root })._reactRootContainer
    : undefined;
}
function setRootContainer(td: unknown, root: Root) {
  if (typeof td === 'object' && td !== null) {
    (td as { _reactRootContainer?: Root })._reactRootContainer = root;
  }
}

// Store dialog states outside of the component to persist across re-renders
const dialogStates = new Map<string, boolean>();

// Renderer factory for attachment button
export const renderAttachmentButton = (queryClient: QueryClient) => {
  if (!queryClient) throw new Error('queryClient is required for renderAttachmentButton');
  return (
    _instance: Handsontable.Core | undefined,
    td: HTMLTableCellElement,
    _row: number,
    _col: number,
    _prop: string | number,
    _value: unknown,
    _cellProperties: Handsontable.CellProperties
  ) => {
    void _col;
    void _prop;
    void _value;
    void _cellProperties;

    // Check if there's already a React root for this td element
    let root = getRootContainer(td);

    if (!root) {
      // Only create a new root if one doesn't exist
      root = createRoot(td);
      setRootContainer(td, root);
    }

    // Create a wrapper component to handle the dialog state
    const AttachmentButton = () => {
      // Get row data to create a unique key for this dialog and fetch fresh data
      const rowDataRaw = _instance?.getSourceDataAtRow?.(_row);
      const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};
      const dialogKey = `${rowData.id || _row}`;
      const vesselId = rowData.vesselId || rowData.id;

      // Use external state that persists across re-renders
      const [open, setOpenInternal] = React.useState(dialogStates.get(dialogKey) || false);

      const setOpen = (newOpen: boolean) => {
        dialogStates.set(dialogKey, newOpen);
        setOpenInternal(newOpen);
      };

      // Get fresh data from the query cache first, then fallback to Handsontable data
      const freshData = queryClient.getQueryData(['export-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;
      const freshAttachments = freshData?.items?.find((item) => item.id === rowData.id)?.attachments;
      const attachments = freshAttachments || _instance?.getDataAtRowProp(_row, 'attachments') || [];

      // console.log("attachments", attachments)
      // console.log("freshAttachments", freshAttachments)
      const itemName = _instance?.getDataAtRowProp(_row, 'itemName') || '';
      const referenceId = rowData.id ?? '';
      const documentReferenceId = rowData.docEntry ?? 0;

      // Callback to refresh the table data after successful upload
      const handleUploadSuccess = () => {
        // Use the already declared rowData and vesselId from the parent scope

        // Invalidate the specific query with the vessel ID to refetch the vessel data with updated attachments
        if (vesselId) {
          queryClient.invalidateQueries({ queryKey: ['export-vessel', vesselId] });
        } else {
          // Fallback to invalidate all export-vessel queries
          queryClient.invalidateQueries({ queryKey: ['export-vessel'] });
        }

        // Simple debounced re-render to avoid multiple rapid updates
        setTimeout(() => {
          if (_instance) {
            try {
              // Get fresh data from the query cache and update Handsontable
              const freshData = queryClient.getQueryData(['export-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;
              if (freshData && freshData.items) {
                const currentItemId = rowData.id;
                const freshItem = freshData.items.find((item) => item.id === currentItemId);
                if (freshItem) {
                  // Update the specific row with fresh data
                  _instance.setDataAtRowProp(_row, 'attachments', freshItem.attachments || []);
                }
              }

              // Single re-render to update the attachment count
              // The dialog state will persist because it's stored externally
              root.render(React.createElement(AttachmentButton));
            } catch (error) {
              console.warn('Error re-rendering cell:', error);
            }
          }
        }, 200); // Single timeout with reasonable delay
      };

      return React.createElement(React.Fragment, null, [
        React.createElement(Button, {
          key: 'button',
          size: 'xs',
          variant: 'success',
          type: 'button',
          onClick: () => setOpen(true),
          'aria-label': 'View Attachments',
          children: `Attachment (${attachments?.length || 0})`
        }),
        React.createElement(AttachmentDialog, {
          key: 'dialog',
          open: open,
          onOpenChange: setOpen,
          attachments: attachments || [],
          title: `Attachments - ${itemName || 'Item'}`,
          queryClient,
          referenceId,
          documentReferenceId,
          defaultTabName: 'SHIPPING',
          docType: 'Export',
          transType: 'ExportDetails',
          tabName: 'SHIPPING',
          onUploadSuccess: handleUploadSuccess,
        })
      ]);
    };

    root.render(React.createElement(AttachmentButton));
    return td;
  };
};

export const getExportVesselColumns = (tenants: MasterTenantDto[], businessPartners: BusinessPartnerDto[], queryClient: QueryClient) => {
  // Extract tenant names for autocomplete source
  const tenantNames = tenants.map(t => t.name ?? '').filter(name => name !== '');

  // Extract business partner names for autocomplete source
  const businessPartnerNames = businessPartners.map(bp => bp.name ?? '').filter(name => name !== '');

  return [
    { data: 'id', title: 'Id', type: 'text', width: 200 },
    { data: 'docEntry', title: 'DocEntry', type: 'text', width: 200 },
    { data: 'concurrencyStamp', title: 'concurrencyStamp', type: 'text', width: 200 },
    {
      data: 'tenant',
      title: 'Tenant',
      type: 'autocomplete',
      width: 140,
      source: tenantNames,
      strict: false,
      allowInvalid: false,
      trimDropdown: false,
      visibleRows: 6,
    },
    {
      data: 'businessPartner',
      title: 'Business Partner',
      type: 'autocomplete',
      width: 300,
      source: businessPartnerNames,
      strict: false,
      allowInvalid: false,
      trimDropdown: false,
      visibleRows: 6,
    },
    { data: 'itemName', title: 'Item Name', type: 'text', width: 250 },
    { data: 'grossWeight', title: 'Gross Weight', type: 'numeric', width: 100 },
    { data: 'unitWeight', title: 'Unit Weight', type: 'text', width: 100 },
    { data: 'letterNo', title: 'Letter No', type: 'text', width: 120 },
    { data: 'letterDate', title: 'Letter Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'shippingInstructionNo', title: 'Shipping Instruction No', type: 'text', width: 180 },
    { data: 'shippingInstructionDate', title: 'Shipping Instruction Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 180, correctFormat: true },
    // { data: 'status', title: 'Status', type: 'text', width: 120 },
    // { data: 'regType', title: 'RegType', type: 'text', width: 120 },
    // { data: 'noBl', title: 'No BL', type: 'text', width: 120 },
    // { data: 'dateBl', title: 'Date BL', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    // { data: 'ajuNo', title: 'AJU No', type: 'text', width: 120 },
    // { data: 'regNo', title: 'Reg No', type: 'text', width: 120 },
    // { data: 'regDate', title: 'Reg Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    // { data: 'sppbNo', title: 'SPPB No', type: 'text', width: 120 },
    // { data: 'sppbDate', title: 'SPPB Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    // { data: 'sppdNo', title: 'SPPD No', type: 'text', width: 120 },
    // { data: 'sppdDate', title: 'SPPD Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'remarks', title: 'Remark', type: 'text', width: 120 },
    { data: 'attachments', title: 'Attachment', width: 100, renderer: renderAttachmentButton(queryClient), readOnly: true, filterable: false },
  ];
}; 